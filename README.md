# Instant TeX

A minimalistic LaTeX math formula editor for macOS with real-time rendering.

![Instant TeX Screenshot](screenshot.png)

## Features

- **Real-time LaTeX Rendering**: See your math formulas rendered instantly as you type
- **Clean Interface**: Split-panel design with resizable editor and preview
- **Advanced Editor Features**:
  - Syntax highlighting for LaTeX
  - Word highlighting on selection
  - Cmd+D for multi-selection (select next occurrence)
  - Automatic indentation
  - Comment toggling with Cmd+/
- **KaTeX Integration**: Fast and accurate math rendering
- **macOS Native**: Built with Electron for seamless macOS integration

## Installation

### Prerequisites
- Node.js (v16 or later)
- npm

### Setup
1. Clone or download this repository
2. Install dependencies:
   ```bash
   npm install
   ```

### Running the App
```bash
npm start
```

### Building for Distribution
```bash
npm run build
```

This will create a distributable `.dmg` file in the `dist` folder.

## Usage

1. **Writing Math**: Simply type LaTeX math formulas in the left editor panel
2. **Live Preview**: The right panel shows the rendered math in real-time
3. **Examples**:
   - `a^2 + b^2 = c^2` (Pythagorean theorem)
   - `\frac{-b \pm \sqrt{b^2-4ac}}{2a}` (Quadratic formula)
   - `\int_0^\infty e^{-x^2} dx` (Gaussian integral)
   - `\sum_{i=1}^n x_i` (Summation)

## Keyboard Shortcuts

- **⌘D**: Select next occurrence of selected text
- **⌘/**: Toggle comment (add/remove %)
- **Tab**: Indent selected lines
- **⌘S**: Save file (to be implemented)
- **⌘O**: Open file (to be implemented)

## Supported LaTeX

Instant TeX supports most common LaTeX math commands through KaTeX:

- Basic operations: `+`, `-`, `*`, `/`, `=`
- Superscripts and subscripts: `^`, `_`
- Fractions: `\frac{numerator}{denominator}`
- Square roots: `\sqrt{expression}`
- Integrals: `\int`, `\iint`, `\iiint`
- Summations: `\sum`, `\prod`
- Greek letters: `\alpha`, `\beta`, `\gamma`, etc.
- Functions: `\sin`, `\cos`, `\tan`, `\log`, `\ln`
- Matrices: `\begin{pmatrix}...\end{pmatrix}`
- And much more!

## Development

### Project Structure
```
instant-tex/
├── src/
│   ├── main.js          # Electron main process
│   ├── index.html       # App UI structure
│   ├── styles.css       # App styling
│   ├── renderer.js      # Renderer process logic
│   └── latex-mode.js    # Custom LaTeX syntax highlighting
├── assets/
│   └── icon.svg         # App icon
├── package.json         # Dependencies and build config
└── README.md           # This file
```

### Technologies Used
- **Electron**: Cross-platform desktop app framework
- **CodeMirror 6**: Modern code editor with syntax highlighting
- **KaTeX**: Fast math typesetting library
- **CSS Grid/Flexbox**: Responsive layout

## Contributing

Feel free to submit issues and enhancement requests!

## License

MIT License - see LICENSE file for details.
