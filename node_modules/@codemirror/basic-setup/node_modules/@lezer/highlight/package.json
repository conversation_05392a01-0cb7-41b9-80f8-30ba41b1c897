{"name": "@lezer/highlight", "version": "0.16.0", "description": "Highlighting system for Lezer parse trees", "main": "dist/index.cjs", "type": "module", "exports": {"import": "./dist/index.js", "require": "./dist/index.cjs"}, "module": "dist/index.js", "types": "dist/highlight.d.ts", "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "devDependencies": {"rollup": "^2.52.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "rollup-plugin-typescript2": "^0.30.0", "typescript": "^4.3.4"}, "dependencies": {"@lezer/common": "^0.16.0"}, "files": ["dist"], "repository": {"type": "git", "url": "https://github.com/lezer-parser/highlight.git"}, "scripts": {"watch": "rollup -w -c rollup.config.js", "prepare": "rollup -c rollup.config.js"}}