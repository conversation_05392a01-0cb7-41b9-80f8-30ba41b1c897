## 0.20.3 (2022-05-25)

### New features

Diagnostic objects may now have a `renderMessage` method to render their message to the DOM.

## 0.20.2 (2022-05-02)

### New features

The package now exports the `LintSource` function type.

The new `markerFilter` and `tooltipFilter` options to `linter` and `lintGutter` allow more control over which diagnostics are visible and which have tooltips.

## 0.20.1 (2022-04-22)

### Bug fixes

Hide lint tooltips when the document is changed.

## 0.20.0 (2022-04-20)

### Breaking changes

Update dependencies to 0.20.0

## 0.19.6 (2022-03-04)

### Bug fixes

Fix a bug where hovering over the icons in the lint gutter would sometimes fail to show a tooltip or show the tooltip for another line.

## 0.19.5 (2022-02-25)

### Bug fixes

Make sure the lint gutter tooltips are positioned under their icon, even when the line is wrapped.

## 0.19.4 (2022-02-25)

### Bug fixes

Fix an issue where an outdated marker could stick around on the lint gutter after all diagnostics were removed.

### New features

Add a `hoverTime` option to the lint gutter. Change default hover time to 300

## 0.19.3 (2021-11-09)

### New features

Export a function `lintGutter` which returns an extension that installs a gutter marking lines with diagnostics.

The package now exports the effect used to update the diagnostics (`setDiagnosticsEffect`).

## 0.19.2 (2021-09-29)

### Bug fixes

Fix a bug where reconfiguring the lint source didn't restart linting.

## 0.19.1 (2021-09-17)

### Bug fixes

Prevent decorations that cover just a line break from being invisible by showing a widget instead of range for them.

### New features

The `diagnosticCount` method can now be used to determine whether there are active diagnostics.

## 0.19.0 (2021-08-11)

### Breaking changes

Update dependencies to 0.19.0

## 0.18.6 (2021-08-08)

### Bug fixes

Fix a crash in the key handler of the lint panel when no diagnostics are available.

## 0.18.5 (2021-08-07)

### Bug fixes

Fix an issue that caused `openLintPanel` to not actually open the panel when ran before the editor had any lint state loaded.

### New features

The package now exports a `forceLinting` function that forces pending lint queries to run immediately.

## 0.18.4 (2021-06-07)

### Bug fixes

Multiple `linter` extensions can now be added to an editor without disrupting each other.

Fix poor layout on lint tooltips due to changes in @codemirror/tooltip.

## 0.18.3 (2021-05-10)

### Bug fixes

Fix a regression where using `setDiagnostics` when linting hadn't been abled yet ignored the first set of diagnostics.

## 0.18.2 (2021-04-16)

### Bug fixes

Newlines in line messages are now shown as line breaks to the user.

### New features

You can now pass a delay option to `linter` to configure how long it waits before calling the linter.

## 0.18.1 (2021-03-15)

### Bug fixes

Adjust to current @codemirror/panel and @codemirror/tooltip interfaces.

## 0.18.0 (2021-03-03)

### Bug fixes

Make sure action access keys are discoverable for screen reader users.

Selection in the lint panel should now be properly visible to screen readers.

## 0.17.1 (2021-01-06)

### New features

The package now also exports a CommonJS module.

## 0.17.0 (2020-12-29)

### Breaking changes

First numbered release.

