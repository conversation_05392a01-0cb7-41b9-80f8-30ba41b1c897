{"name": "@codemirror/state", "version": "0.20.1", "description": "Editor state data structures for the CodeMirror code editor", "scripts": {"test": "cm-runtests", "prepare": "cm-buildhelper src/index.ts"}, "keywords": ["editor", "code"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://marijnhaverbeke.nl"}, "type": "module", "main": "dist/index.cjs", "exports": {"import": "./dist/index.js", "require": "./dist/index.cjs"}, "types": "dist/index.d.ts", "module": "dist/index.js", "sideEffects": false, "license": "MIT", "devDependencies": {"@codemirror/buildhelper": "^0.1.5"}, "repository": {"type": "git", "url": "https://github.com/codemirror/state.git"}}