{"name": "instant-tex", "version": "1.0.0", "description": "A minimalistic LaTeX text editor with real-time equation rendering", "main": "src/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "dist": "electron-builder --publish=never"}, "keywords": ["latex", "editor", "electron", "katex", "math"], "author": "", "license": "MIT", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.0.0"}, "dependencies": {"codemirror": "^6.0.1", "@codemirror/view": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/commands": "^6.0.0", "@codemirror/search": "^6.0.0", "@codemirror/language": "^6.0.0", "@codemirror/lang-javascript": "^6.0.0", "@codemirror/theme-one-dark": "^6.0.0", "@lezer/highlight": "^1.0.0", "katex": "^0.16.0"}, "build": {"appId": "com.instanttex.app", "productName": "Instant TeX", "directories": {"output": "dist"}, "files": ["src/**/*", "node_modules/**/*", "package.json"], "mac": {"category": "public.app-category.productivity", "target": "dmg", "icon": "assets/icon.icns"}}}