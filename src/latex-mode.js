// Custom LaTeX syntax highlighting for CodeMirror 6
import { LanguageSupport, LRLanguage } from '@codemirror/language';
import { styleTags, tags as t } from '@lezer/highlight';

// Define LaTeX syntax patterns
const latexHighlighting = styleTags({
  'Command': t.keyword,
  'Environment': t.typeName,
  'MathDelimiter': t.bracket,
  'Comment': t.comment,
  'String': t.string,
  'Number': t.number,
  'Brace': t.brace,
  'Bracket': t.squareBracket
});

// Simple LaTeX parser for basic highlighting
const latexLanguage = LRLanguage.define({
  name: 'latex',
  parser: {
    // This is a simplified parser - in a real implementation,
    // you'd want a more sophisticated grammar
    tokenize(input, pos) {
      const tokens = [];
      let i = pos;
      
      while (i < input.length) {
        const char = input[i];
        
        // Comments
        if (char === '%') {
          const start = i;
          while (i < input.length && input[i] !== '\n') i++;
          tokens.push({ type: 'Comment', from: start, to: i });
          continue;
        }
        
        // Commands
        if (char === '\\') {
          const start = i;
          i++; // Skip backslash
          while (i < input.length && /[a-zA-Z]/.test(input[i])) i++;
          if (i > start + 1) {
            tokens.push({ type: 'Command', from: start, to: i });
            continue;
          }
        }
        
        // Math delimiters
        if (char === '$') {
          const start = i;
          i++;
          if (i < input.length && input[i] === '$') {
            i++; // Double dollar
          }
          tokens.push({ type: 'MathDelimiter', from: start, to: i });
          continue;
        }
        
        // Braces
        if (char === '{' || char === '}') {
          tokens.push({ type: 'Brace', from: i, to: i + 1 });
          i++;
          continue;
        }
        
        // Brackets
        if (char === '[' || char === ']') {
          tokens.push({ type: 'Bracket', from: i, to: i + 1 });
          i++;
          continue;
        }
        
        i++;
      }
      
      return tokens;
    }
  },
  languageData: {
    commentTokens: { line: '%' },
    indentOnInput: /^\s*\\(begin|end)\b/,
    wordChars: 'a-zA-Z\\'
  }
});

// Apply highlighting
latexLanguage.parser.configure({
  props: [latexHighlighting]
});

// Export the language support
export function latex() {
  return new LanguageSupport(latexLanguage);
}

// Alternative: Simple regex-based highlighting for immediate use
export const simpleLatexHighlighting = [
  // Commands
  { regex: /\\[a-zA-Z]+\*?/, token: 'keyword' },
  // Math environments
  { regex: /\$\$?/, token: 'bracket' },
  // Comments
  { regex: /%.*/, token: 'comment' },
  // Braces and brackets
  { regex: /[{}]/, token: 'brace' },
  { regex: /[\[\]]/, token: 'bracket' }
];
