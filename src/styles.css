/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #1e1e1e;
    color: #d4d4d4;
    overflow: hidden;
    /* macOS-specific styling */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

#app {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Main container with split layout */
.container {
    display: flex;
    height: 100vh;
    background-color: #1e1e1e;
}

/* Panel styles */
.editor-panel,
.preview-panel {
    display: flex;
    flex-direction: column;
    min-width: 300px;
    background-color: #252526;
    border: 1px solid #3e3e42;
}

.editor-panel {
    flex: 1;
    border-right: none;
}

.preview-panel {
    flex: 1;
    border-left: none;
}

/* Panel headers */
.panel-header {
    background-color: #2d2d30;
    padding: 8px 16px;
    border-bottom: 1px solid #3e3e42;
    display: flex;
    align-items: center;
    min-height: 35px;
}

.panel-header h3 {
    font-size: 13px;
    font-weight: 500;
    color: #cccccc;
}

/* Editor container */
#editor-container {
    flex: 1;
    overflow: hidden;
}

/* Preview container */
#preview-container {
    flex: 1;
    overflow: auto;
    background-color: #ffffff;
    color: #000000;
}

.preview-content {
    padding: 20px;
    line-height: 1.6;
    max-width: none;
}

.placeholder {
    color: #666;
    text-align: center;
    margin: 50px 20px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.placeholder h3 {
    color: #1976d2;
    margin-bottom: 15px;
}

.placeholder h4 {
    color: #388e3c;
    margin: 20px 0 10px 0;
    font-size: 16px;
}

.placeholder .examples,
.placeholder .shortcuts {
    text-align: left;
    margin: 20px 0;
}

.placeholder ul {
    list-style-type: none;
    padding-left: 0;
}

.placeholder li {
    margin: 8px 0;
    padding: 5px 10px;
    background-color: #f5f5f5;
    border-radius: 4px;
    border-left: 3px solid #1976d2;
}

.placeholder code {
    background-color: #e8e8e8;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'SF Mono', Monaco, monospace;
    color: #d73a49;
}

.placeholder kbd {
    background-color: #fafbfc;
    border: 1px solid #d1d5da;
    border-bottom-color: #c6cbd1;
    border-radius: 3px;
    box-shadow: inset 0 -1px 0 #c6cbd1;
    color: #444d56;
    display: inline-block;
    font-family: 'SF Mono', Monaco, monospace;
    font-size: 11px;
    line-height: 10px;
    padding: 3px 5px;
    vertical-align: middle;
}

/* Resizer */
.resizer {
    width: 4px;
    background-color: #3e3e42;
    cursor: col-resize;
    position: relative;
    z-index: 10;
}

.resizer:hover {
    background-color: #007acc;
}

/* CodeMirror overrides */
.cm-editor {
    height: 100%;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    font-size: 14px;
}

.cm-focused {
    outline: none;
}

.cm-scroller {
    font-family: inherit;
}

/* KaTeX styling in preview */
.katex {
    font-size: 1.1em;
}

.katex-display {
    margin: 1em 0;
}

/* Math line styling */
.math-line {
    margin: 15px 0;
    text-align: center;
}

.math-error-line {
    background-color: #ffebee;
    color: #c62828;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #ef5350;
    margin: 10px 0;
    font-family: monospace;
}

.math-error-line small {
    display: block;
    margin-top: 5px;
    font-size: 12px;
}

.error {
    background-color: #ffebee;
    color: #c62828;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #ef5350;
    margin: 10px 0;
}

/* Preview content styling */
.preview-content h1 {
    color: #1976d2;
    border-bottom: 2px solid #1976d2;
    padding-bottom: 5px;
    margin-bottom: 15px;
}

.preview-content h2 {
    color: #388e3c;
    border-bottom: 1px solid #388e3c;
    padding-bottom: 3px;
    margin-top: 25px;
    margin-bottom: 10px;
}

.preview-content h3 {
    color: #f57c00;
    margin-top: 20px;
    margin-bottom: 8px;
}

.preview-content p {
    margin-bottom: 10px;
    text-align: justify;
}

.preview-content em {
    color: #666;
}

/* Scrollbar styling for webkit browsers */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #1e1e1e;
}

::-webkit-scrollbar-thumb {
    background: #424242;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #4f4f4f;
}

/* Responsive design */
@media (max-width: 800px) {
    .container {
        flex-direction: column;
    }
    
    .resizer {
        width: 100%;
        height: 4px;
        cursor: row-resize;
    }
}
